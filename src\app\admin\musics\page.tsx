"use client";

import { useGetMusics } from "@schemas/Music/music-query";
import { useGetNatureSounds } from "@schemas/Natural/nature-sound-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Search } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { MusicRow } from "./_components/music-card";
import { NatureSoundRow } from "./_components/nature-sound-card";
import { MusicFormSheet } from "./_components/music-form-sheet";
import { NatureSoundFormSheet } from "./_components/nature-sound-form-sheet";
import { AudioPlayer } from "./_components/audio-player";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default function MusicPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("music");
  const [isMusicFormOpen, setIsMusicFormOpen] = useState(false);
  const [isNatureSoundFormOpen, setIsNatureSoundFormOpen] = useState(false);

  const { data: musics, isLoading: isLoadingMusic, refetch: refetchMusics } = useGetMusics();
  const { data: natureSounds, isLoading: isLoadingNatureSounds, refetch: refetchNatureSounds } = useGetNatureSounds();

  const filteredMusics = musics?.filter((music) =>
    music.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredNatureSounds = natureSounds?.filter((sound) =>
    sound.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleMusicSuccess = () => {
    refetchMusics();
  };

  const handleNatureSoundSuccess = () => {
    refetchNatureSounds();
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <div className="flex flex-col gap-8">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Sound Library</h1>
            <p className="text-muted-foreground mt-2">
              Manage your music and nature sounds collection
            </p>
          </div>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <div className="relative w-full sm:w-[300px]">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search sounds..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
            <Button
              onClick={() =>
                activeTab === "music"
                  ? setIsMusicFormOpen(true)
                  : setIsNatureSoundFormOpen(true)
              }
            >
              <Plus className="mr-2 h-4 w-4" />
              Add {activeTab === "music" ? "Music" : "Nature Sound"}
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList>
            <TabsTrigger value="music">
              Music ({musics?.length || 0})
            </TabsTrigger>
            <TabsTrigger value="nature">
              Nature Sounds ({natureSounds?.length || 0})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="music">
            {isLoadingMusic ? (
              <div className="space-y-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-32 w-full" />
              </div>
            ) : filteredMusics?.length === 0 ? (
              <div className="text-center py-12">
                <h3 className="text-lg font-semibold">No music tracks found</h3>
                <p className="text-muted-foreground mt-2">
                  {searchQuery
                    ? "Try adjusting your search"
                    : "Add some music to get started"}
                </p>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Source</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Rating</TableHead>
                      <TableHead>Genres</TableHead>
                      <TableHead>Note</TableHead>
                      <TableHead>Public</TableHead>
                      <TableHead>Copyright</TableHead>
                      <TableHead>Creator Type</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMusics?.map((music) => (
                      <MusicRow key={music.id} music={music} />
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>

          <TabsContent value="nature">
            {isLoadingNatureSounds ? (
              <div className="space-y-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-32 w-full" />
              </div>
            ) : filteredNatureSounds?.length === 0 ? (
              <div className="text-center py-12">
                <h3 className="text-lg font-semibold">No nature sounds found</h3>
                <p className="text-muted-foreground mt-2">
                  {searchQuery
                    ? "Try adjusting your search"
                    : "Add some nature sounds to get started"}
                </p>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredNatureSounds?.map((sound) => (
                      <NatureSoundRow key={sound.id} natureSound={sound} />
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Forms */}
      <MusicFormSheet
        open={isMusicFormOpen}
        onOpenChange={setIsMusicFormOpen}
        onSuccess={handleMusicSuccess}
      />
      <NatureSoundFormSheet
        open={isNatureSoundFormOpen}
        onOpenChange={setIsNatureSoundFormOpen}
        onSuccess={handleNatureSoundSuccess}
      />
      
      {/* Audio Player */}
      <AudioPlayer />
    </div>
  );
}