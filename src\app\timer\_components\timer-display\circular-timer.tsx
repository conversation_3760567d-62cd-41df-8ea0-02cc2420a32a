'use client';

import { cn } from '@/lib/utils';
import { TimerColorPreset } from '@/lib/pomodoro-store';
import { getTimerTextColor, getTimerTextColorValue, getTimerTextShadow, getPhaseLabelColor } from './common/constants';
import type { FontSize } from './use-timer-resize';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface CircularTimerProps {
  formattedTime: string;
  phaseLabel: string;
  isRunning: boolean;
  currentPhase: string;
  pomodoroCount: number;
  timerColor: TimerColorPreset;
  showControls: boolean;
  progressPercentage: number;
  timerSize: {
    width?: number;
    timeScale?: number;
  } | null;
  fontSize: FontSize | null;
  timerSettings: {
    longBreakInterval?: number;
    sessionsBeforeLongBreak?: number;
  };
  onSwitchPhase: (direction: 'prev' | 'next') => void;
  currentTask: { id: string; title: string } | null;
}

export function CircularTimer({
  formattedTime,
  phaseLabel,
  isRunning,
  currentPhase,
  pomodoroCount,
  timerColor,
  showControls,
  progressPercentage,
  timerSize,
  fontSize,
  timerSettings,
  onSwitchPhase,
  currentTask
}: CircularTimerProps) {
  // Helper function for phase color - used only for the small indicator dot
  const getPhaseColor = () => {
    switch(currentPhase) {
      case 'pomodoro': return 'bg-blue-500';
      case 'shortBreak': return 'bg-emerald-500';
      case 'longBreak': return 'bg-purple-500';
      default: return 'bg-blue-500';
    }
  };

  // Function to get the session label
  const getSessionLabel = () => {
    if (currentPhase === 'shortBreak') {
      return `${pomodoroCount} of ${timerSettings.longBreakInterval || timerSettings.sessionsBeforeLongBreak} sessions`;
    } else if (currentPhase === 'longBreak') {
      return `Long Break`;
    } else {
      // For focus sessions, show task title if available, otherwise show session count
      return currentTask 
        ? currentTask.title 
        : `${pomodoroCount} of ${timerSettings.longBreakInterval || timerSettings.sessionsBeforeLongBreak} sessions`;
    }
  };

  // Get the color for the ring based on the selected text color
  const getRingColor = () => {
    // For white, use phase-specific colors for the progress ring (like progress bar)
    if (timerColor === 'white') {
      switch(currentPhase) {
        case 'pomodoro': return '#3b82f6'; // blue
        case 'shortBreak': return '#10b981'; // green
        case 'longBreak': return '#a855f7'; // purple
        default: return '#3b82f6';
      }
    }

    // Map text colors to appropriate ring colors
    const colorMap: Record<TimerColorPreset, string> = {
      white: '#ffffff', // fallback, but handled above
      blue: '#3b82f6',
      green: '#10b981',
      yellow: '#fbbf24',
      red: '#ef4444',
      purple: '#a855f7',
      indigo: '#6366f1',
      pink: '#ec4899',
      orange: '#f97316'
    };

    return colorMap[timerColor];
  };

  // Calculate the SVG parameters for the progress ring
  const containerWidth = timerSize?.width || 200;
  // Increase the size to 80% to fill more of the available space
  const size = Math.min(containerWidth * 0.8, containerWidth - 24);
  // Increase stroke width to make the circle thicker
  const strokeWidth = Math.max(3.5, size * 0.03); // Thicker stroke width
  const radius = (size / 2) - (strokeWidth * 2); // Account for stroke width
  const circumference = 2 * Math.PI * radius;
  const progress = circumference - (progressPercentage / 100) * circumference;
  const ringColor = getRingColor();

  return (
    <div className="relative w-full flex flex-col items-center justify-center">
      {/* Container that provides proper spacing for phase label when visible */}
      <div className={cn(
        "w-full flex justify-center",
        showControls ? "h-6 mb-1" : "h-0"
      )}>
        {/* Phase Label with Switcher - Only visible on hover */}
        <div
          className={cn(
            "font-medium text-center z-10",
            "flex items-center justify-center gap-1.5",
            "timer-control-fade",
            showControls ? "show" : "hide pointer-events-none",
            getPhaseLabelColor(timerColor) // Apply phase label color
          )}
          style={{
            textShadow: '0 1px 2px rgba(0,0,0,0.15)',
            // Use responsive font sizing similar to minimal timer for consistency
            fontSize: fontSize?.phase || (timerSize?.width
              ? `${Math.max(0.5, Math.min(0.7, 0.62 * Math.pow(timerSize.width / 320, 0.25)))}rem`
              : 'clamp(0.55rem, 1vw, 0.7rem)'),
            letterSpacing: '0.04em',
            textTransform: 'uppercase',
            animationDelay: '30ms'
          }}
        >
          {/* Left chevron for phase switching */}
          {showControls && (
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-5 w-5 mr-1 flex items-center justify-center p-0 transition-all duration-250 ease-out cursor-pointer",
                "rounded-full bg-black/30 hover:bg-black/40 border-white/10",
                "text-white focus:ring-1 focus:ring-white/30 focus:ring-offset-0 shadow-sm",
                "opacity-100 hover:scale-105 active:scale-95"
              )}
              onClick={() => onSwitchPhase('prev')}
              title="Previous phase"
              aria-label="Switch to previous phase"
            >
              <ChevronLeft className="h-3 w-3 text-white" />
            </Button>
          )}

          {/* Phase indicator */}
          <div className={cn(
            "w-2 h-2 rounded-full transition-properties ease-out ring-2 ring-white/30",
            getPhaseColor()
          )} />
          <span className={getPhaseLabelColor(timerColor)}>{phaseLabel}</span>

          {/* Task indicator - small dot when task is active */}
          {currentTask && currentPhase === 'pomodoro' && (
            <div className="w-1.5 h-1.5 rounded-full bg-green-400 ring-1 ring-green-300/50 ml-1" 
                 title="Task focused session" />
          )}

          {/* Right chevron for phase switching */}
          {showControls && (
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-5 w-5 ml-1 flex items-center justify-center p-0 transition-all duration-250 ease-out cursor-pointer",
                "rounded-full bg-black/30 hover:bg-black/40 border-white/10",
                "text-white focus:ring-1 focus:ring-white/30 focus:ring-offset-0 shadow-sm",
                "opacity-100 hover:scale-105 active:scale-95"
              )}
              onClick={() => onSwitchPhase('next')}
              title="Next phase"
              aria-label="Switch to next phase"
            >
              <ChevronRight className="h-3 w-3 text-white" />
            </Button>
          )}
        </div>
      </div>

      {/* Circular Timer Container - filling more of the card */}
      <div
        className={cn(
          "relative flex items-center justify-center mx-auto",
          showControls ? "mt-0" : "mt-1"
        )}
        style={{
          width: size,
          height: size
        }}
      >
        {/* SVG for circular progress */}
        <svg
          width={size}
          height={size}
          viewBox={`0 0 ${size} ${size}`}
          className="transform -rotate-90"
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="transparent"
            stroke="rgba(255, 255, 255, 0.1)"
            strokeWidth={strokeWidth}
          />

          {/* Progress circle using the selected text color */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="transparent"
            stroke={ringColor}
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            strokeDasharray={circumference}
            strokeDashoffset={progress}
            className="transition-all duration-300 ease-in-out"
            style={{
              filter: 'drop-shadow(0 0 4px rgba(0, 0, 0, 0.2))',
              opacity: 1 // Always use full opacity for consistent visibility
            }}
          />
        </svg>

        {/* Timer Display in Center */}
        <div
          className="absolute inset-0 flex flex-col items-center justify-center"
        >
          {/* Time Display */}
          <div
            className={cn(
              "font-bold text-center transition-properties ease-out",
              "font-mono", // Ensure monospace for consistent rendering
              getTimerTextColor(timerColor) // Apply timer text color
            )}
            style={{
              textShadow: getTimerTextShadow(timerColor),
              color: getTimerTextColorValue(timerColor),
              // Use responsive font sizing similar to minimal timer
              fontSize: fontSize?.time || 'clamp(2.2rem, 4.5vw, 3.2rem)',
              lineHeight: 1.1,
              // Remove artificial scaling limit to allow full responsive scaling
              transform: timerSize?.timeScale ? `scale3d(${timerSize.timeScale}, ${timerSize.timeScale}, 1)` : undefined,
              transformOrigin: 'center center',
              display: 'block',
              fontVariantNumeric: 'tabular-nums',
              width: 'min-content',
              minWidth: '4.8ch',
              margin: '0 auto',
              // Add performance optimizations for text rendering
              WebkitFontSmoothing: 'antialiased',
              WebkitBackfaceVisibility: 'hidden' as const,
              backfaceVisibility: 'hidden' as const,
              contain: 'paint' as const
            }}
            aria-live="polite"
          >
            {formattedTime}
          </div>

          {/* Session Counter */}
          <div
            className={cn(
              "text-center mt-0.5",
              "flex items-center justify-center",
              getPhaseLabelColor(timerColor) // Apply phase label color for session info
            )}
            style={{
              letterSpacing: '0.02em',
              // Make session counter responsive to timer scaling
              fontSize: timerSize?.timeScale
                ? `${Math.max(0.45, Math.min(0.65, 0.5 * timerSize.timeScale))}rem`
                : '0.5rem',
              opacity: 1 // Use full opacity for better visibility
            }}
          >
            <div className="flex items-center justify-center">
              <span className={cn(
                "px-1 py-0.5 rounded-md transition-properties ease-out font-medium",
                // Remove hardcoded background colors for cleaner appearance
                "bg-transparent"
              )}
              style={{
                // Make text size responsive to scaling
                fontSize: timerSize?.timeScale
                  ? `${Math.max(8, Math.min(12, 9 * timerSize.timeScale))}px`
                  : '9px'
              }}>
                {getSessionLabel()}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}