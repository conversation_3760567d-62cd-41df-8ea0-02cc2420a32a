'use client';

import { useCallback, useState, useRef, useMemo, useEffect, memo } from 'react';
import { usePomodoro } from '@/hooks/usePomodoro';
import { usePomodoroStore, TimerPhase } from '@/lib/pomodoro-store';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useTimerResize } from './use-timer-resize';
import { ResizeHandle } from './resize-handle';
import { positionClasses, getBackgroundOpacity } from './common/constants';
import PlayPauseButton from './controls/play-pause-button';
import { TimerGlobalStyles } from './styles/global-styles';
import { useTimerDragManager } from './timer-drag-manager';
import { TimerMainDisplay } from './main-display';
import { CircularTimer } from './circular-timer';
import { TimerProgressBar } from './timer-progress-bar';
import type { CustomPosition } from './common/types';
import { SignInPrompt } from './sign-in-prompt';
import { useUserStore } from '@/store/userStore';
import { useRouter } from 'next/navigation';

// Wrap the TimerDisplay component with memo to prevent unnecessary re-renders
export const TimerDisplay = memo(function TimerDisplay() {
  const router = useRouter();
  const { isAuthenticated, preferences, updatePreferences } = useUserStore();
  const [showSignInPrompt, setShowSignInPrompt] = useState(false);

  // Helper function to safely get dismissal status
  const isSignInPromptDismissed = useMemo(() => {
    return preferences?.ui?.dismissedSignInPrompt ?? false;
  }, [preferences?.ui?.dismissedSignInPrompt]);

  // Handle session completion for unauthenticated users
  const handleSessionComplete = useCallback((phase: string, isUserAuthenticated: boolean) => {
    // Only show prompt for unauthenticated users who haven't dismissed it
    if (!isUserAuthenticated && !isSignInPromptDismissed) {
      // Only show for focus sessions (pomodoro), not breaks
      if (phase === 'pomodoro') {
        setShowSignInPrompt(true);
      }
    }
  }, [isSignInPromptDismissed]);

  const {
    formattedTime,
    phaseLabel,
    progressPercentage,
    isRunning,
    startTimer,
    pauseTimer,
    resetTimer,
    currentPhase,
    pomodoroCount
  } = usePomodoro({ onSessionComplete: handleSessionComplete });

  // Get the values directly since usePomodoro now returns computed values
  const timeDisplay = formattedTime;
  const currentPhaseLabel = phaseLabel;
  const currentProgress = progressPercentage;

  // Get properties from the pomodoro store
  const timerPosition = usePomodoroStore((state) => state.timerPosition);
  const timerSettings = usePomodoroStore((state) => state.timerSettings);
  const isFullscreen = usePomodoroStore((state) => state.isFullscreen);
  const timerColor = usePomodoroStore((state) => state.timerColor);
  const timerOpacity = usePomodoroStore((state) => state.timerOpacity);
  const autoStartPomodoros = usePomodoroStore((state) => state.autoStartPomodoros);
  const autoFullscreen = usePomodoroStore((state) => state.autoFullscreen);
  const timerUIStyle = usePomodoroStore((state) => state.timerUIStyle || 'default');
  const setTimerPosition = usePomodoroStore((state) => state.setTimerPosition);
  const skipBreak = usePomodoroStore((state) => state.skipBreak);
  const currentTask = usePomodoroStore((state) => state.currentTask);
  const showProgressBar = usePomodoroStore((state) => state.showProgressBar);


  const [showControls, setShowControls] = useState(false);
  const [customPosition, setCustomPosition] = useState<CustomPosition | null>(null);
  const timerRef = useRef<HTMLDivElement>(null);
  const timerDisplayRef = useRef<HTMLDivElement>(null);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isHoveringRef = useRef(false);
  // Add mobile-specific state
  const [isMobile, setIsMobile] = useState(false);
  const [touchStartTime, setTouchStartTime] = useState(0);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
                           window.innerWidth <= 768;
      setIsMobile(isMobileDevice);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Use our custom resize hook
  const {
    isResizing,
    timerSize,
    fontSize,
    showResizeHandles,
    isResizeHandleHovered,
    isResizePreview,
    startResize,
    handleResizeMove,
    endResize,
    showHandles,
    hideHandles,
    handleResizeHandleMouseEnter,
    handleResizeHandleMouseLeave,
    applyPreset,
    cleanup: cleanupResize
  } = useTimerResize(timerRef);

  // Use the drag manager hook
  const {
    isDragging,
    showPositionIndicator,
    handleDragStartMouse,
    handleDragStartTouch,
    handleDoubleClick,
    adjustTimerPosition,
    setShowPositionIndicator
  } = useTimerDragManager({
    timerRef,
    customPosition,
    setCustomPosition,
    isResizing,
    showControls,
    setShowControls,
    hideHandles,
    timerSize,
    setTimerPosition
  });

  // Reset timer position function
  const resetTimerPosition = useCallback(() => {
    // Clear custom position to use predefined positions
    setCustomPosition(null);

    // Reset to default position (bottom-right)
    setTimerPosition('bottom-right');

    // Apply small preset for consistent size
    applyPreset('small');

    // Show position indicator feedback
    setShowPositionIndicator(true);
    setTimeout(() => setShowPositionIndicator(false), 1500);
  }, [setCustomPosition, setTimerPosition, applyPreset, setShowPositionIndicator]);

  // Handle fullscreen change
  useEffect(() => {
    // When fullscreen state changes, adjust timer position
    if (customPosition) {
      requestAnimationFrame(adjustTimerPosition);
    }
  }, [isFullscreen, adjustTimerPosition, customPosition]);

  // Handle window resize events with debouncing
  useEffect(() => {
    if (!customPosition) return;

    let resizeTimeout: NodeJS.Timeout;

    const handleResize = () => {
      // Clear the timeout if there's one pending
      if (resizeTimeout) {
        clearTimeout(resizeTimeout);
      }

      // Set a new timeout for performance
      resizeTimeout = setTimeout(() => {
        requestAnimationFrame(adjustTimerPosition);
      }, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      if (resizeTimeout) {
        clearTimeout(resizeTimeout);
      }
    };
  }, [customPosition, adjustTimerPosition]);

  // Memoized function to update control visibility
  const updateControlVisibility = useCallback((shouldShow: boolean, forceUpdate = false) => {
    if (shouldShow === showControls && !forceUpdate) return; // No change needed unless forced

    // Clean up existing timeout if any
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }

    if (shouldShow) {
      setShowControls(true);
      showHandles();
    } else {
      // Only hide controls if we're not dragging or resizing, or if force update
      if (forceUpdate || (!isDragging && !isResizing)) {
        setShowControls(false);
        hideHandles();
      }
    }
  }, [showControls, showHandles, hideHandles, isDragging, isResizing]);

  // Handle mouse enter with debounced approach
  const handleMouseEnter = useCallback(() => {
    isHoveringRef.current = true;

    // Skip if already showing controls
    if (showControls) return;

    // Add a small delay to prevent flashes during quick mouse movements
    hoverTimeoutRef.current = setTimeout(() => {
      if (isHoveringRef.current && timerRef.current?.matches(':hover')) {
        updateControlVisibility(true);
      }
    }, 40);
  }, [showControls, updateControlVisibility]);

  const handleMouseLeave = useCallback((e: React.MouseEvent) => {
    isHoveringRef.current = false;

    // Don't hide controls if we're dragging or resizing
    if (isDragging || isResizing) return;

    // Skip if already hiding controls
    if (!showControls) return;

    // Get the event related target (what we're moving to)
    const relatedTarget = e.relatedTarget;

    // Check if we're moving to a child element or outside the timer
    if (relatedTarget instanceof Node && timerRef.current?.contains(relatedTarget)) {
      // We're still inside the timer component, don't hide controls
      return;
    }

    // Add a short delay to prevent flickering when moving near edges
    hoverTimeoutRef.current = setTimeout(() => {
      // Double-check we're not hovering before hiding
      if (!isHoveringRef.current && timerRef.current && !timerRef.current.matches(':hover')) {
        updateControlVisibility(false);
      }
    }, 100);
  }, [isDragging, isResizing, showControls, updateControlVisibility]);

  // Enhanced touch start handler to prevent page refresh and improve controls
  const handleTouchStartControls = useCallback((e: React.TouchEvent) => {
    const currentTime = Date.now();
    setTouchStartTime(currentTime);

    // Check if this is a drag start by looking for button/resize handle targets
    const target = e.target as HTMLElement;
    const isDragTarget = !target.closest('button') && !target.closest('[data-resize-handle]');
    
    if (isDragTarget) {
      // Add timer-interacting class to enable strict touch restrictions
      if (timerRef.current) {
        timerRef.current.classList.add('timer-interacting');
      }
      
      // For potential drag operations, prevent default to avoid page refresh
      // This specifically prevents pull-to-refresh when dragging from top
      e.preventDefault();
    }

    // Handle control visibility
    if (!showControls) {
      // Show controls immediately on touch
      updateControlVisibility(true);

      // Clear any existing timeout
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }

      // Set timeout to hide controls after touch interaction
      hoverTimeoutRef.current = setTimeout(() => {
        if (!isDragging && !isResizing) {
          updateControlVisibility(false);
        }
      }, 4000);
    }
  }, [showControls, updateControlVisibility, isDragging, isResizing]);

  // Add touch end handler to ensure proper control hiding
  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    const touchDuration = Date.now() - touchStartTime;
    
    // Remove timer-interacting class when touch ends
    if (timerRef.current) {
      timerRef.current.classList.remove('timer-interacting');
    }
    
    // If it was a quick tap (not a drag), handle control visibility
    if (touchDuration < 300 && !isDragging && !isResizing) {
      const target = e.target as HTMLElement;
      
      // Don't hide controls if touching buttons or resize handles
      if (!target.closest('button') && !target.closest('[data-resize-handle]')) {
        // Clear existing timeout
        if (hoverTimeoutRef.current) {
          clearTimeout(hoverTimeoutRef.current);
        }

        // Set new timeout to hide controls
        hoverTimeoutRef.current = setTimeout(() => {
          if (!isDragging && !isResizing) {
            updateControlVisibility(false);
          }
        }, 3000);
      }
    }
  }, [touchStartTime, isDragging, isResizing, updateControlVisibility]);

  // Global touch event handler for mobile to hide controls when touching outside
  useEffect(() => {
    if (!isMobile) return;

    const handleGlobalTouch = (e: TouchEvent) => {
      // Only handle if controls are currently visible
      if (!showControls || isDragging || isResizing) return;

      const target = e.target as HTMLElement;
      
      // Check if touch is outside timer component
      if (timerRef.current && !timerRef.current.contains(target)) {
        // Clear any existing timeout
        if (hoverTimeoutRef.current) {
          clearTimeout(hoverTimeoutRef.current);
        }

        // Hide controls immediately when touching outside
        updateControlVisibility(false);
      }
    };

    // Add passive listener for better performance
    document.addEventListener('touchstart', handleGlobalTouch, { passive: true });

    return () => {
      document.removeEventListener('touchstart', handleGlobalTouch);
    };
  }, [isMobile, showControls, isDragging, isResizing, updateControlVisibility]);

  // Handle mouse resize - optimized with memoization
  const handleResizeStartMouse = useCallback((e: React.MouseEvent) => {
    // Prevent default to avoid text selection
    e.preventDefault();
    startResize(e);
  }, [startResize]);

  // Handle touch resize - optimized
  const handleResizeStartTouch = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    startResize(e);
  }, [startResize]);

  // Effect to add mouse move and up event listeners for resize - optimized with throttling
  useEffect(() => {
    if (isResizing) {
      let lastFrameTime = 0;
      const FRAME_RATE = 1000 / 60; // Target 60 FPS

      const handleResizeMoveMouse = (e: MouseEvent) => {
        if (!isResizing) return;

        e.preventDefault();

        // Throttling to ensure smooth animation at 60fps target
        const now = performance.now();
        const elapsed = now - lastFrameTime;

        if (elapsed > FRAME_RATE) {
          lastFrameTime = now;

          requestAnimationFrame(() => {
            if (isResizing) { // Double-check we're still resizing
              handleResizeMove(e.clientX);
            }
          });
        }
      };

      // Use passive: false for mouseup to prevent potential browser issues
      window.addEventListener('mousemove', handleResizeMoveMouse, { passive: false });
      window.addEventListener('mouseup', endResize);

      return () => {
        window.removeEventListener('mousemove', handleResizeMoveMouse);
        window.removeEventListener('mouseup', endResize);
      };
    }
  }, [isResizing, handleResizeMove, endResize]);

  // Keyboard navigation for resizing - remains mostly the same
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle keyboard events if the timer has focus
      if (!document.activeElement || !timerRef.current?.contains(document.activeElement)) {
        return;
      }

      if (e.key === 'Escape') {
        if (isResizing) {
          endResize();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isResizing, endResize]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
      cleanupResize();
    };
  }, [cleanupResize]);

  // Handle timer toggle
  const handleTimerToggle = useCallback(() => {
    if (isRunning) {
      pauseTimer();
    } else {
      startTimer();
    }
  }, [isRunning, pauseTimer, startTimer]);

  // Keyboard event handler for accessibility
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'Enter':
      case ' ':
        handleTimerToggle();
        e.preventDefault();
        break;
    }
  }, [handleTimerToggle]);

  // Wrapper functions to prevent dragging when settings dialog is open
  const handleDragStartMouseWrapper = useCallback((e: React.MouseEvent) => {
    // Check if the target is a control element (button, resize handle, etc.)
    const target = e.target as HTMLElement;
    const isControlElement = target.closest('button') || 
                           target.closest('[data-resize-handle]') || 
                           target.closest('[role="button"]') ||
                           target.hasAttribute('data-resize-handle');
    
    // Don't allow dragging if clicking on actual control elements
    if (isControlElement) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    
    handleDragStartMouse(e);
  }, [handleDragStartMouse]);

  const handleDragStartTouchWrapper = useCallback((e: React.TouchEvent) => {
    // Check if the target is a control element (button, resize handle, etc.)
    const target = e.target as HTMLElement;
    const isControlElement = target.closest('button') || 
                           target.closest('[data-resize-handle]') || 
                           target.closest('[role="button"]') ||
                           target.hasAttribute('data-resize-handle');
    
    // Don't allow dragging if touching actual control elements
    if (isControlElement) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    
    handleDragStartTouch(e);
  }, [handleDragStartTouch]);

  const handleDoubleClickWrapper = useCallback((e: React.MouseEvent) => {
    // Check if the target is a control element
    const target = e.target as HTMLElement;
    const isControlElement = target.closest('button') || 
                           target.closest('[data-resize-handle]') || 
                           target.closest('[role="button"]') ||
                           target.hasAttribute('data-resize-handle');
    
    // Don't allow double click actions if clicking on control elements
    if (isControlElement) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    
    handleDoubleClick(e);
  }, [handleDoubleClick]);

  // After the handleKeyDown callback, add the handleSwitchPhase function back
  const handleSwitchPhase = useCallback((direction: 'prev' | 'next') => {
    const phases: TimerPhase[] = ['pomodoro', 'shortBreak', 'longBreak'];
    const currentIndex = phases.indexOf(currentPhase as TimerPhase);

    let newIndex: number;
    if (direction === 'prev') {
      // Loop back to the end if at the beginning
      newIndex = currentIndex <= 0 ? phases.length - 1 : currentIndex - 1;
    } else {
      // Loop back to the beginning if at the end
      newIndex = currentIndex >= phases.length - 1 ? 0 : currentIndex + 1;
    }

    // Get the new phase
    const newPhase = phases[newIndex];
    let newTimeRemaining: number;

    // Set the appropriate time based on the new phase
    switch (newPhase) {
      case 'pomodoro':
        newTimeRemaining = timerSettings.pomodoroMinutes * 60;
        break;
      case 'shortBreak':
        newTimeRemaining = timerSettings.shortBreakMinutes * 60;
        break;
      case 'longBreak':
        newTimeRemaining = timerSettings.longBreakMinutes * 60;
        break;
      default:
        newTimeRemaining = timerSettings.pomodoroMinutes * 60;
    }

    // Determine if we need to update pomodoroCount
    // We only increment when going from break to pomodoro
    const currentPomodoroCount = usePomodoroStore.getState().pomodoroCount;
    const newPomodoroCount =
      (currentPhase !== 'pomodoro' && newPhase === 'pomodoro')
        ? (currentPhase === 'longBreak' ? 1 : currentPomodoroCount + 1)
        : currentPomodoroCount;

    // Update state through store
    usePomodoroStore.setState({
      currentPhase: newPhase,
      timeRemaining: newTimeRemaining,
      isRunning: false,
      pomodoroCount: newPomodoroCount
    });
  }, [currentPhase, timerSettings]);

  // Calculate stable styles to prevent expensive recalculations on hover
  const cardStyles = useMemo(() => {
    // Get viewport dimensions and calculate bottom position
    // This ensures the timer stays at the bottom edge even during animations
    const topPosition = customPosition?.top;

    // We no longer need to handle isAtBottomEdge here since we're using predefined positions
    // when the timer is at the bottom edge

    // Calculate opacity based on user settings and ensure minimum contrast
    const adjustedOpacity = getBackgroundOpacity(timerOpacity) / 100;

    // Precompute background colors to avoid recalculation during animation
    const transparentBg = 'transparent';

    // Precompute shadows to avoid recalculation during animation
    const noShadow = 'none';
    const controlsShadow = '0 8px 32px rgba(0, 0, 0, 0.28)';
    const runningShadow = '0 4px 16px rgba(0, 0, 0, 0.18)';
    const normalShadow = '0 4px 18px rgba(0, 0, 0, 0.22)';

    return {
      transform: isResizePreview ? 'scale3d(1.008, 1.008, 1)' : 'scale3d(1, 1, 1)',
      // Use will-change only when necessary to reduce GPU memory consumption
      willChange: isDragging
        ? 'transform, left, top'
        : isResizing
          ? 'width'
          : showControls
            ? 'opacity, transform'
            : 'auto',
      top: topPosition,
      left: customPosition?.left,
      // Disable transition during drag/resize for better performance
      transition: isDragging || isResizing
        ? 'none'
        : 'transform 220ms cubic-bezier(0.16, 1, 0.3, 1), opacity 220ms cubic-bezier(0.16, 1, 0.3, 1), background-color 220ms ease-out, box-shadow 220ms ease-out',
      width: timerSize?.width,
      // Optimized background calculations - precomputed values
      backgroundColor: timerOpacity === 0
        ? transparentBg
        : transparentBg, // Always use transparent background to prevent dimming effect
      backgroundImage: showControls
        ? undefined // Remove gradient when controls are shown
        : isRunning
          ? 'linear-gradient(135deg, rgba(255,255,255,0.02), rgba(255,255,255,0))'
          : undefined,
      // Optimized shadow calculations - precomputed values
      boxShadow: timerOpacity < 10
        ? noShadow
        : showControls
          ? controlsShadow
          : isRunning
            ? runningShadow
            : normalShadow,
      // Reduce blur amount for better performance, but keep it active when showControls is true to ensure readability
      backdropFilter: timerOpacity === 0
        ? 'none'
        : showControls
          ? `blur(${Math.max(4, Math.min(12, 10 * adjustedOpacity))}px)` // Enhanced blur for better readability without background
          : `blur(${Math.max(3, Math.min(10, 8 * adjustedOpacity))}px)`,
      // Add hardware acceleration with properly typed properties
      WebkitBackfaceVisibility: 'hidden' as const,
      backfaceVisibility: 'hidden' as const,
      transformStyle: 'preserve-3d' as const,
      contain: 'paint' as const,
    };
  }, [
    showControls,
    isResizePreview,
    isDragging,
    isResizing,
    customPosition?.top,
    customPosition?.left,
    timerSize?.width,
    isRunning,
    timerOpacity,
  ]);

  // Pre-compute card class names to avoid recalculation on render
  const cardClassNames = useMemo(() => cn(
    "fixed z-20 transition-properties ease-out border-0", // z-20 ensures timer is above video (z-0) but below navigation controls (z-50)
    // Apply transparent effect with the specified classes
    "bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-neutral-600 dark:text-neutral-300 backdrop-blur-[1px]",
    // Conditionally apply border based on timerOpacity (similar to boxShadow logic)
    timerOpacity >= 10 ? "border border-neutral-400/20" : "border-0",
    showControls
      ? "backdrop-blur-lg shadow-xl p-3"
      : isRunning
        ? "backdrop-blur-md shadow-md p-2"
        : "backdrop-blur-md shadow-md p-2",
    !customPosition ? positionClasses[timerPosition] : '',
    "touch-manipulation rounded-xl",
    isDragging ? "opacity-90 cursor-grabbing select-none !transition-none" : "cursor-pointer",
    isResizePreview ? "!ring-2 ring-offset-1 ring-offset-background/3 ring-white/12" : "",
    showPositionIndicator ? "ring-2 ring-white/15 ring-offset-1 ring-offset-background/3" : ""
  ), [
    showControls,
    isRunning,
    customPosition,
    timerPosition,
    isDragging,
    isResizePreview,
    showPositionIndicator,
    timerOpacity
  ]);

  // Precompute content class names and styles
  const contentClassNames = useMemo(() => cn(
    "transition-properties ease-out",
    // Reduced padding for circular timer to minimize empty space
    timerUIStyle === 'circular' ? "px-1 py-1.5 flex justify-center items-center" : ""
  ), [timerUIStyle]);

  // Compute content styles separately for better performance
  const contentStyles = useMemo(() => {
    const baseOpacity = 1; // Always use full opacity to prevent dimming effect

    // Calculate enhanced padding for larger timer scales
    if (timerSize?.timeScale && timerSize.timeScale > 1.3) {
      const scaleFactor = timerSize.timeScale - 1.3;
      const basePaddingX = showControls ? 1.5 : isRunning ? 0.5 : 1;
      const basePaddingY = showControls ? 3 : isRunning ? 1.5 : 2;

      const enhancedPaddingX = Math.max(basePaddingX, Math.min(basePaddingX + scaleFactor * 0.8, basePaddingX * 1.6));
      const enhancedPaddingY = Math.max(basePaddingY, Math.min(basePaddingY + scaleFactor * 1.2, basePaddingY * 1.8));

      return {
        opacity: baseOpacity,
        paddingLeft: `${enhancedPaddingX * 0.25}rem`,
        paddingRight: `${enhancedPaddingX * 0.25}rem`,
        paddingTop: `${enhancedPaddingY * 0.25}rem`,
        paddingBottom: `${enhancedPaddingY * 0.25}rem`
      };
    }

    // Default padding for smaller timer scales
    return {
      opacity: baseOpacity,
      paddingLeft: showControls ? '0.375rem' : isRunning ? '0.125rem' : '0.25rem',
      paddingRight: showControls ? '0.375rem' : isRunning ? '0.125rem' : '0.25rem',
      paddingTop: showControls ? '0.75rem' : isRunning ? '0.375rem' : '0.5rem',
      paddingBottom: showControls ? '0.75rem' : isRunning ? '0.375rem' : '0.5rem'
    };
  }, [showControls, isRunning, timerSize?.timeScale]);

  // Add an effect to apply small preset on component mount
  useEffect(() => {
    applyPreset('small');

    // Set default timer appearance to Minimal preset (white color, 45% opacity)
    const setTimerColor = usePomodoroStore.getState().setTimerColor;
    const setTimerOpacity = usePomodoroStore.getState().setTimerOpacity;

    setTimerColor('white');
    setTimerOpacity(45);
  }, [applyPreset]);

  // Sign-in prompt handlers
  const handleSignIn = useCallback(() => {
    setShowSignInPrompt(false);
    router.push('/auth/sign-in');
  }, [router]);

  const handleCloseSignInPrompt = useCallback(() => {
    setShowSignInPrompt(false);
  }, []);

  // Auto-start pomodoro timer when page loads if setting is enabled
  useEffect(() => {
    if (autoStartPomodoros && currentPhase === 'pomodoro' && !isRunning) {
      // Get the manuallyPaused state from the store
      const manuallyPaused = usePomodoroStore.getState().manuallyPaused;

      // Only auto-start if the timer was not manually paused
      if (!manuallyPaused) {
        // Small delay to ensure everything is loaded
        const autoStartTimer = setTimeout(() => {
          startTimer();
        }, 500);

        return () => clearTimeout(autoStartTimer);
      }
    }
  }, [autoStartPomodoros, currentPhase, isRunning, startTimer]);

  return (
   <>
    <TimerGlobalStyles />
    <Card
     ref={timerRef}
     className={cn(
       cardClassNames,
       "timer-focus-ring"
     )}
      style={{
        ...cardStyles,
        ...(showControls && !isDragging && !isResizing
          ? { animation: 'timerScaleIn 220ms cubic-bezier(0.16, 1, 0.3, 1) forwards' }
          : {})
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onTouchStart={handleTouchStartControls}
      onTouchEnd={handleTouchEnd}
      onMouseDown={handleDragStartMouseWrapper}
      onTouchStartCapture={handleDragStartTouchWrapper}
      onKeyDown={handleKeyDown}
      onDoubleClick={handleDoubleClickWrapper}
      role="region"
      aria-label={`${currentPhaseLabel} timer with ${timeDisplay} remaining`}
      tabIndex={0}
    >
      {/* Help tooltip for drag/position - shown briefly on resize/reposition */}
      {showPositionIndicator && (
        <div className="absolute inset-0 pointer-events-none z-50 flex items-center justify-center">
          <div className="text-xs text-white font-medium bg-black/75 backdrop-blur-sm px-3 py-2 rounded-lg shadow-lg border border-white/12 animate-fade-in">
            Timer position reset
          </div>
        </div>
      )}

      {/* Single resize handle in the bottom-right corner */}
      {showResizeHandles && (
        <ResizeHandle
          onMouseEnter={handleResizeHandleMouseEnter}
          onMouseLeave={handleResizeHandleMouseLeave}
          onMouseDown={handleResizeStartMouse}
          onTouchStart={handleResizeStartTouch}
          isHovered={isResizeHandleHovered}
          isResizing={isResizing}
          showControls={showControls}
        />
      )}

      {/* Resize preview hint - shows when hovering resize handle */}
      {isResizePreview && (
        <div className="absolute inset-0 pointer-events-none z-30 flex items-center justify-center">
          <div className="text-xs text-white font-medium bg-black/50 backdrop-blur-md px-3 py-2 rounded-lg shadow-md border border-white/12 animate-fade-in">
            Click and drag to resize
          </div>
        </div>
      )}

      <CardContent className={contentClassNames} style={contentStyles}>
        <div className="flex flex-col items-center">
          {/* Main Timer Display with enhanced visual elements */}
          <div ref={timerDisplayRef} className="w-full">
            {/* Conditionally render timer UI based on timerUIStyle */}
            {timerUIStyle === 'circular' ? (
              <CircularTimer
                formattedTime={timeDisplay}
                phaseLabel={currentPhaseLabel}
                isRunning={isRunning}
                currentPhase={currentPhase}
                pomodoroCount={pomodoroCount}
                timerColor={timerColor}
                showControls={showControls}
                progressPercentage={currentProgress}
                timerSize={timerSize}
                fontSize={fontSize}
                timerSettings={timerSettings}
                onSwitchPhase={handleSwitchPhase}
                currentTask={currentTask}
              />
            ) : (
              <TimerMainDisplay
                formattedTime={timeDisplay}
                phaseLabel={currentPhaseLabel}
                isRunning={isRunning}
                currentPhase={currentPhase}
                pomodoroCount={pomodoroCount}
                timerColor={timerColor}
                showControls={showControls}
                timerSize={timerSize}
                fontSize={fontSize}
                timerSettings={timerSettings}
                onSwitchPhase={handleSwitchPhase}
                currentTask={currentTask}
              />
            )}
          </div>

          {/* Start/Stop Button - Moved below timer display and above progress bar for better accessibility */}
          <div
            className={cn(
              "flex justify-center items-center",
              "timer-control-fade",
              showControls && !isDragging ? "show mt-2 mb-2" : "hide pointer-events-none h-0 overflow-hidden"
            )}
            style={{
              marginTop: showControls && timerSize?.timeScale && timerSize.timeScale > 1.3
                ? `${Math.min(2, (timerSize.timeScale - 1) * 2)}rem`
                : showControls ? '0.6rem' : '0',
              marginBottom: showControls ? undefined : '0',
              animationDelay: '60ms'
            }}
          >
            <PlayPauseButton
              isRunning={isRunning}
              onClick={handleTimerToggle}
              onReset={resetTimer}
              timerColor={timerColor}
            />
          </div>

          {/* Only show progress bar for default UI style and when enabled */}
          {timerUIStyle === 'default' && showProgressBar && (
            <TimerProgressBar
              progressPercentage={currentProgress}
              isRunning={isRunning}
              showControls={showControls}
              timerColor={timerColor}
              timerSize={timerSize}
            />
          )}


        </div>
      </CardContent>
    </Card>

    {/* Sign-in prompt for unauthenticated users */}
    <SignInPrompt
      isVisible={showSignInPrompt}
      onClose={handleCloseSignInPrompt}
      onSignIn={handleSignIn}
    />
   </>
  );
});