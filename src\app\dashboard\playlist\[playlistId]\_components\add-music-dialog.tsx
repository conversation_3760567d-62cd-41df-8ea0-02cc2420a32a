"use client"

import { useState, useMemo, useEffect, useRef, useCallback } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import { Slider } from "@/components/ui/slider"
import {
  Search,
  Plus,
  Check,
  Music2,
  Star,
  Filter,
  X,
  Headphones,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Shield,
  Globe
} from "lucide-react"
import { useGetMusics } from "@schemas/Music/music-query"
import { useAddMusicToMusicPlaylistUser, useGetMusicPlaylistUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { toast } from "sonner"



interface AddMusicDialogProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  playlistId: string
  playlistName: string
}

export function AddMusicDialog({ isOpen, onOpenChange, playlistId, playlistName }: AddMusicDialogProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedGenres, setSelectedGenres] = useState<string[]>([])
  const [addedMusicIds, setAddedMusicIds] = useState<Set<string>>(new Set())
  const [hoveredMusicId, setHoveredMusicId] = useState<string | null>(null)
  const [addingMusicIds, setAddingMusicIds] = useState<Set<string>>(new Set())
  const [isFiltering, setIsFiltering] = useState(false)

  // Format source name for display
  const formatSourceName = (source: string) => {
    switch (source) {
      case 'EPIDEMICSOUND':
        return 'Epidemic Sound';
      case 'PIXABAY':
        return 'Pixabay';
      case 'SUNO':
        return 'Suno';
      case 'YOUTUBE':
        return 'YouTube';
      case 'OTHER':
        return 'Other';
      default:
        return source.charAt(0) + source.slice(1).toLowerCase();
    }
  }

  // Audio player state
  const [currentPreviewTrack, setCurrentPreviewTrack] = useState<{
    id: string
    title: string
    src: string
    genres?: string[]
  } | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [volume, setVolume] = useState(80)
  const [isMuted, setIsMuted] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  const { data: allMusics = [], isLoading } = useGetMusics({ isPublic: true })
  const { data: currentPlaylist, isLoading: isLoadingPlaylist } = useGetMusicPlaylistUser(playlistId)
  const addMusicToPlaylist = useAddMusicToMusicPlaylistUser()


  // Get IDs of music already in the playlist
  const existingMusicIds = useMemo(() => {
    return currentPlaylist?.musics?.map(music => music.id) || []
  }, [currentPlaylist?.musics])

  // Get all unique genres from available music (excluding already added)
  const allGenres = useMemo(() => {
    const genreSet = new Set<string>()
    allMusics.forEach(music => {
      // Only include genres from music not already in the playlist
      if (!existingMusicIds.includes(music.id) && music.genres) {
        music.genres.forEach(genre => genreSet.add(genre))
      }
    })
    return Array.from(genreSet).sort()
  }, [allMusics, existingMusicIds])

  // Filter musics based on search, genre, and exclude already added music
  const filteredMusics = useMemo(() => {
    return allMusics.filter(music => {
      // Exclude music already in the playlist
      const notInPlaylist = !existingMusicIds.includes(music.id)

      const matchesSearch = searchQuery === "" ||
        music.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        music.source?.toLowerCase().includes(searchQuery.toLowerCase())

      const matchesGenre = selectedGenres.length === 0 ||
        (music.genres && selectedGenres.every(genre => music.genres?.includes(genre as any)))

      return notInPlaylist && matchesSearch && matchesGenre
    })
  }, [allMusics, existingMusicIds, searchQuery, selectedGenres])

  const handleGenreToggle = (genre: string) => {
    setIsFiltering(true)
    setSelectedGenres(prev =>
      prev.includes(genre)
        ? prev.filter(g => g !== genre)
        : [...prev, genre]
    )
    // Brief loading state for smooth UX
    setTimeout(() => setIsFiltering(false), 150)
  }

  const handleAddMusic = async (musicId: string) => {
    // Prevent multiple simultaneous requests for the same music
    if (addingMusicIds.has(musicId) || addedMusicIds.has(musicId)) {
      return
    }

    setAddingMusicIds(prev => new Set(prev).add(musicId))

    try {
      await addMusicToPlaylist.mutateAsync({
        musicPlaylistUserId: playlistId,
        musicIds: [musicId]
      })
      setAddedMusicIds(prev => new Set(prev).add(musicId))
      toast.success("Track added to playlist")
    } catch (error) {
      console.error(error)
      toast.error("Failed to add track")
    } finally {
      setAddingMusicIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(musicId)
        return newSet
      })
    }
  }

  const handlePlayMusic = (music: any) => {
    const track = {
      id: music.id,
      title: music.title,
      src: music.src || `https://example.com/music/${music.id}.mp3`,
      genres: music.genres
    }

    if (currentPreviewTrack?.id === music.id) {
      // If already playing this track, toggle play/pause
      if (isPlaying) {
        audioRef.current?.pause()
      } else {
        audioRef.current?.play()
      }
    } else {
      // Play new track
      setCurrentPreviewTrack(track)
    }
  }

  const clearFilters = () => {
    setSearchQuery("")
    setSelectedGenres([])
  }

  // Audio player functions
  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime)
    }
  }, [])

  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration)
    }
  }, [])

  const handlePlayPause = useCallback(() => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
    } else {
      audioRef.current.play().catch(error => {
        console.error("Play failed:", error)
      })
    }
  }, [isPlaying])

  const handleVolumeToggle = useCallback(() => {
    setIsMuted(!isMuted)
  }, [isMuted])

  const handleSeek = useCallback((value: number[]) => {
    const newTime = value[0]
    if (audioRef.current) {
      audioRef.current.currentTime = newTime
      setCurrentTime(newTime)
    }
  }, [])

  const handleClosePreview = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause()
    }
    setCurrentPreviewTrack(null)
    setIsPlaying(false)
  }, [])

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // Audio management effect
  useEffect(() => {
    if (!currentPreviewTrack?.src) {
      setIsPlaying(false)
      return
    }

    // Create audio element if it doesn't exist
    if (!audioRef.current) {
      audioRef.current = new Audio()

      // Set up event listeners
      audioRef.current.addEventListener("play", () => setIsPlaying(true))
      audioRef.current.addEventListener("pause", () => setIsPlaying(false))
      audioRef.current.addEventListener("ended", () => {
        setIsPlaying(false)
        setCurrentPreviewTrack(null)
      })
      audioRef.current.addEventListener("timeupdate", handleTimeUpdate)
      audioRef.current.addEventListener("loadedmetadata", handleLoadedMetadata)
    }

    // Update audio source if it changed
    if (audioRef.current.src !== currentPreviewTrack.src) {
      audioRef.current.src = currentPreviewTrack.src
      audioRef.current.load()
    }

    // Play the audio
    const playPromise = audioRef.current.play()
    if (playPromise !== undefined) {
      playPromise.catch(error => {
        console.error("Autoplay prevented:", error)
        setIsPlaying(false)
      })
    }

    // Update volume & muted state
    audioRef.current.volume = isMuted ? 0 : volume / 100

    // Cleanup function
    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener("play", () => setIsPlaying(true))
        audioRef.current.removeEventListener("pause", () => setIsPlaying(false))
        audioRef.current.removeEventListener("ended", () => {
          setIsPlaying(false)
          setCurrentPreviewTrack(null)
        })
        audioRef.current.removeEventListener("timeupdate", handleTimeUpdate)
        audioRef.current.removeEventListener("loadedmetadata", handleLoadedMetadata)
      }
    }
  }, [currentPreviewTrack?.src, isMuted, volume, handleTimeUpdate, handleLoadedMetadata])

  // Update volume when it changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume / 100
    }
  }, [volume, isMuted])

  // Cleanup when dialog closes
  useEffect(() => {
    if (!isOpen) {
      handleClosePreview()
    }
  }, [isOpen, handleClosePreview])

  const getGenreColor = (genre: string, isSelected: boolean = false) => {
    if (isSelected) {
      // Subtle red tones for selected genres
      return 'bg-red-100 text-red-800 dark:bg-red-900/40 dark:text-red-300 border-red-300 dark:border-red-700'
    }

    const colors = {
      'ambient': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border-blue-200 dark:border-blue-800',
      'classical': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300 border-purple-200 dark:border-purple-800',
      'jazz': 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 border-amber-200 dark:border-amber-800',
      'electronic': 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-300 border-cyan-200 dark:border-cyan-800',
      'rock': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 border-red-200 dark:border-red-800',
      'pop': 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300 border-pink-200 dark:border-pink-800',
      'indie': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border-green-200 dark:border-green-800',
      'folk': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300 border-orange-200 dark:border-orange-800',
    }
    return colors[genre.toLowerCase() as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300 border-gray-200 dark:border-gray-800'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="!max-w-[50vw] !w-[50vw] !h-[85vh] flex flex-col p-0 sm:!max-w-[50vw] md:!max-w-[50vw] lg:!max-w-[50vw] xl:!max-w-[50vw] 2xl:!max-w-[50vw]" style={{ maxWidth: '50vw', width: '50vw', height: '85vh' }}>
        <DialogHeader className="p-6 pb-4 border-b border-border/50">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30">
              <Plus className="h-5 w-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div className="flex-1">
              <DialogTitle className="text-xl font-semibold">Add Music to Playlist</DialogTitle>
              <DialogDescription className="mt-1">
                Add tracks to <span className="font-medium text-foreground">"{playlistName}"</span>
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 flex flex-col min-h-0">
          {/* Search and Filter Section */}
          <div className="p-4 space-y-3 border-b border-border/50 bg-muted/20">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tracks by title or source..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-10 text-sm bg-background focus:ring-2 focus:ring-orange-500/20"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSearchQuery("")}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 hover:bg-muted"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>

            {/* Genre Filters */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Filter className="h-3.5 w-3.5 text-muted-foreground" />
                  <span className="text-sm font-medium">Genres</span>
                  {selectedGenres.length > 1 && (
                    <span className="text-xs text-muted-foreground ml-1">(must have all)</span>
                  )}
                  {selectedGenres.length > 0 && (
                    <Badge variant="secondary" className="text-xs h-5">
                      {selectedGenres.length}
                    </Badge>
                  )}
                </div>
                {(searchQuery || selectedGenres.length > 0) && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearFilters}
                    className="h-7 px-2 text-xs"
                  >
                    <X className="h-3 w-3 mr-1" />
                    Clear
                  </Button>
                )}
              </div>
              
              <div className="flex flex-wrap gap-1.5">
                {allGenres.map(genre => {
                  const isSelected = selectedGenres.includes(genre)
                  return (
                    <Badge
                      key={genre}
                      variant={isSelected ? "default" : "outline"}
                      className={cn(
                        "cursor-pointer transition-all duration-200 hover:scale-105 text-xs px-2 py-1 h-6",
                        isSelected
                          ? getGenreColor(genre, true)
                          : "hover:bg-red-50 border-muted-foreground/20 hover:border-red-200 hover:text-red-700 dark:hover:bg-red-950/20 dark:hover:text-red-400 dark:hover:border-red-800"
                      )}
                      onClick={() => handleGenreToggle(genre)}
                    >
                      {genre}
                    </Badge>
                  )
                })}
              </div>
            </div>

            {/* Results Summary */}
            <div className="flex items-center justify-between text-xs text-muted-foreground pt-1">
              <span>
                <strong className="text-foreground">{filteredMusics.length}</strong> track{filteredMusics.length !== 1 ? 's' : ''} available
                {existingMusicIds.length > 0 && (
                  <span className="ml-2 text-orange-600 dark:text-orange-400">
                    ({existingMusicIds.length} already in playlist)
                  </span>
                )}
              </span>
              {addedMusicIds.size > 0 && (
                <span className="text-green-600 dark:text-green-400 font-medium">
                  <strong>{addedMusicIds.size}</strong> added
                </span>
              )}
            </div>
          </div>

          {/* Music List */}
          <ScrollArea className="flex-1 overflow-y-auto">
            <div className="p-4">
              {isLoading || isLoadingPlaylist || isFiltering ? (
                <div className="space-y-2">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <motion.div
                      key={i}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.2, delay: i * 0.05 }}
                      className="flex items-center gap-3 p-3 rounded-lg border border-border/50 bg-muted/10"
                    >
                      <Skeleton className="h-10 w-10 rounded-lg animate-pulse" />
                      <div className="flex-1 space-y-1.5">
                        <Skeleton className="h-3.5 w-3/4 animate-pulse" />
                        <div className="flex gap-2">
                          <Skeleton className="h-2.5 w-16 animate-pulse" />
                          <Skeleton className="h-2.5 w-20 animate-pulse" />
                        </div>
                      </div>
                      <div className="flex gap-1.5">
                        <Skeleton className="h-7 w-7 rounded animate-pulse" />
                        <Skeleton className="h-6 w-12 rounded animate-pulse" />
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : filteredMusics.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="text-center py-12 space-y-3"
                >
                  <div className="mx-auto w-16 h-16 rounded-full bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30 flex items-center justify-center">
                    <Music2 className="h-6 w-6 text-orange-500/70" />
                  </div>
                  <div className="space-y-1">
                    <h3 className="text-base font-semibold text-muted-foreground">No tracks found</h3>
                    <p className="text-sm text-muted-foreground max-w-sm mx-auto">
                      {searchQuery || selectedGenres.length > 0
                        ? "Try adjusting your search or filter criteria"
                        : existingMusicIds.length > 0
                        ? "All available tracks are already in this playlist"
                        : "No music tracks are available to add"
                      }
                    </p>
                  </div>
                  {(searchQuery || selectedGenres.length > 0) && (
                    <Button variant="outline" size="sm" onClick={clearFilters} className="hover:bg-red-50 hover:text-red-700 hover:border-red-200 dark:hover:bg-red-950/20 dark:hover:text-red-400 dark:hover:border-red-800">
                      <X className="h-3 w-3 mr-1" />
                      Clear Filters
                    </Button>
                  )}
                </motion.div>
              ) : (
                <div className="space-y-0.5">
                  <AnimatePresence>
                    {filteredMusics.map((music, index) => {
                      const isAdded = addedMusicIds.has(music.id)
                      const isHovered = hoveredMusicId === music.id
                      const isCurrentlyPlaying = currentPreviewTrack?.id === music.id && isPlaying
                      const isAddingThis = addingMusicIds.has(music.id)
                      
                      return (
                        <motion.div
                          key={music.id}
                          layout
                          initial={{ opacity: 0, y: 5 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -5 }}
                          transition={{ duration: 0.1, delay: index * 0.01 }}
                          className={cn(
                            "group flex items-center gap-3 px-3 py-2 rounded-md border transition-all duration-200 cursor-pointer",
                            isAdded && "bg-green-50/50 dark:bg-green-950/20 border-green-200 dark:border-green-800",
                            isCurrentlyPlaying && "bg-orange-50/50 dark:bg-orange-950/20 border-orange-200 dark:border-orange-800",
                            !isAdded && !isCurrentlyPlaying && "border-border/30 hover:border-border hover:bg-muted/20"
                          )}
                          onMouseEnter={() => setHoveredMusicId(music.id)}
                          onMouseLeave={() => setHoveredMusicId(null)}
                        >
                          {/* Music Icon / Play Button */}
                          <div className="w-7 h-7 rounded-md bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/40 dark:to-rose-900/40 flex items-center justify-center shrink-0 relative">
                            {isHovered ? (
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handlePlayMusic(music)
                                }}
                                className="w-7 h-7 rounded-md hover:bg-orange-200 dark:hover:bg-orange-800 absolute inset-0"
                              >
                                <Music2 className="h-3.5 w-3.5 text-orange-600 dark:text-orange-400" />
                              </Button>
                            ) : (
                              <Music2 className="h-3.5 w-3.5 text-orange-600 dark:text-orange-400" />
                            )}
                          </div>

                          {/* Music Info */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-1.5">
                              <h4 className="font-medium text-sm truncate">
                                {music.title}
                              </h4>
                              {music.rating && music.rating > 4 && (
                                <Star className="h-2.5 w-2.5 text-yellow-500 fill-current shrink-0" />
                              )}
                            </div>
                            <div className="flex items-center gap-1.5 text-xs text-muted-foreground mt-0.5">
                              <span>11:00</span> {/* Duration placeholder */}
                              {music.genres && music.genres.length > 0 && (
                                <>
                                  <span>•</span>
                                  <span className="truncate">{music.genres[0]}</span>
                                </>
                              )}
                              {music.source && (
                                <>
                                  <span>•</span>
                                  <div className="flex items-center gap-0.5">
                                    <Globe className="h-2.5 w-2.5 shrink-0" />
                                    <span className="truncate">{formatSourceName(music.source)}</span>
                                  </div>
                                </>
                              )}
                              {music.isCopyright !== undefined && (
                                <>
                                  <span>•</span>
                                  <div className="flex items-center gap-0.5">
                                    <Shield className={cn(
                                      "h-2.5 w-2.5 shrink-0",
                                      music.isCopyright ? "text-red-500" : "text-green-500"
                                    )} />
                                    <span className={cn(
                                      "truncate",
                                      music.isCopyright ? "text-red-600 dark:text-red-400" : "text-green-600 dark:text-green-400"
                                    )}>
                                      {music.isCopyright ? "Copyrighted" : "Free"}
                                    </span>
                                  </div>
                                </>
                              )}
                            </div>
                          </div>

                          {/* Actions */}
                          <div className="flex items-center gap-1.5 shrink-0">
                            {/* Play/Headphones Button */}
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation()
                                handlePlayMusic(music)
                              }}
                              className={cn(
                                "w-7 h-7 transition-all hover:bg-muted",
                                isHovered || isCurrentlyPlaying ? "opacity-100" : "opacity-50",
                                isCurrentlyPlaying && "bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400"
                              )}
                            >
                              {isCurrentlyPlaying ? (
                                isPlaying ? <Pause className="h-3.5 w-3.5" /> : <Play className="h-3.5 w-3.5" />
                              ) : (
                                <Headphones className="h-3.5 w-3.5" />
                              )}
                            </Button>

                            {/* Add Button */}
                            <Button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleAddMusic(music.id)
                              }}
                              disabled={isAdded || isAddingThis}
                              size="sm"
                              className={cn(
                                "h-6 px-2.5 text-xs transition-all duration-200",
                                isAdded
                                  ? "bg-green-500 hover:bg-green-600 text-white"
                                  : isAddingThis
                                  ? "bg-gray-400 text-white cursor-not-allowed"
                                  : "bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white"
                              )}
                            >
                              {isAdded ? (
                                <>
                                  <Check className="h-2.5 w-2.5 mr-1" />
                                  Added
                                </>
                              ) : isAddingThis ? (
                                <>
                                  <div className="h-2.5 w-2.5 mr-1 border border-white border-t-transparent rounded-full animate-spin" />
                                  Adding...
                                </>
                              ) : (
                                <>
                                  <Plus className="h-2.5 w-2.5 mr-1" />
                                  Add
                                </>
                              )}
                            </Button>
                          </div>
                        </motion.div>
                      )
                    })}
                  </AnimatePresence>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Audio Player - Sticky Footer */}
        {currentPreviewTrack && (
          <div className="border-t bg-background/95 backdrop-blur-sm p-3 shrink-0">
            <div className="flex items-center gap-3">
              {/* Play/Pause Button */}
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "h-8 w-8 rounded-full shrink-0 transition-all duration-200",
                  isPlaying
                    ? "bg-orange-500 text-white hover:bg-orange-600"
                    : "hover:bg-muted"
                )}
                onClick={handlePlayPause}
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4 ml-0.5" />
                )}
              </Button>

              {/* Track Info */}
              <div className="flex-1 min-w-0">
                <div className="truncate font-medium text-sm">
                  {currentPreviewTrack.title}
                </div>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span>{formatTime(currentTime)}</span>
                  <span>/</span>
                  <span>{formatTime(duration)}</span>
                  {currentPreviewTrack.genres && currentPreviewTrack.genres.length > 0 && (
                    <>
                      <span>•</span>
                      <span>{currentPreviewTrack.genres[0]}</span>
                    </>
                  )}
                </div>
              </div>

              {/* Progress Bar */}
              <div className="hidden md:flex flex-1 max-w-32 items-center">
                <Slider
                  value={[currentTime]}
                  min={0}
                  max={duration || 100}
                  step={0.1}
                  onValueChange={handleSeek}
                  className="cursor-pointer"
                />
              </div>

              {/* Volume Controls */}
              <div className="flex items-center gap-2 shrink-0">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={handleVolumeToggle}
                >
                  {isMuted ? (
                    <VolumeX className="h-3 w-3" />
                  ) : (
                    <Volume2 className="h-3 w-3" />
                  )}
                </Button>
                <Slider
                  value={[isMuted ? 0 : volume]}
                  min={0}
                  max={100}
                  step={1}
                  onValueChange={(value) => {
                    setVolume(value[0])
                    if (value[0] > 0 && isMuted) {
                      setIsMuted(false)
                    }
                  }}
                  className="w-16"
                />
              </div>

              {/* Close Button */}
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={handleClosePreview}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>

            {/* Mobile Progress Bar */}
            <div className="md:hidden mt-2">
              <Slider
                value={[currentTime]}
                min={0}
                max={duration || 100}
                step={0.1}
                onValueChange={handleSeek}
                className="cursor-pointer"
              />
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
} 