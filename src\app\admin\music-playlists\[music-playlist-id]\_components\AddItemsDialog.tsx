import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";

import {
  Check,
  X,
  Music2,
  Video,
  Search,
  Loader2,
  CheckCircle2,
  Filter,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Star
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useState, useRef, useEffect, useCallback, useMemo } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import Image from "next/image";

import { <PERSON>lide<PERSON> } from "@/components/ui/slider";
import { cn } from "@/lib/utils";

interface Music {
  id: string;
  title: string;
  artist?: string;
  src?: string;
  genres?: string[];
  rating?: number;
  duration?: number | null;
  note?: string | null;
}

interface Video {
  id: string;
  title: string;
  thumbnail?: string;
  musicPlaylistId?: string | null;
  naturePlaylistId?: string | null;
}

interface AddItemsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: "music" | "video"; // "music" type represents actual music in this context
  searchQuery: string;
  onSearchChange: (query: string) => void;
  availableItems: Music[] | Video[];
  selectedIds: string[];
  onSelect: (id: string, checked: boolean) => void;
  onSelectAll: () => void;
  onAdd: () => Promise<void>;
  currentPlaylist?: {
    id: string;
    name: string;
    genres?: string[];
    musics?: { genres?: string[] }[];
  };
}

// Local Audio Player Component for Dialog
function DialogAudioPlayer({ 
  currentTrack, 
  onClose 
}: { 
  currentTrack: { id: string; title: string; src: string; artist?: string } | null;
  onClose: () => void;
}) {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(80);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  // Handle time update event
  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  }, []);

  // Handle loaded metadata event
  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  }, []);

  // Handle play/pause toggle
  const handlePlayPause = useCallback(() => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play().catch(error => {
        console.error("Play failed:", error);
      });
    }
  }, [isPlaying]);

  // Handle volume toggle
  const handleVolumeToggle = useCallback(() => {
    setIsMuted(!isMuted);
  }, [isMuted]);

  // Handle seeking in the timeline
  const handleSeek = useCallback((value: number[]) => {
    const newTime = value[0];
    if (audioRef.current) {
      audioRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  }, []);

  // Create/update audio element and set up event listeners
  useEffect(() => {
    if (!currentTrack?.src) {
      setIsPlaying(false);
      return;
    }

    // Create audio element if it doesn't exist
    if (!audioRef.current) {
      audioRef.current = new Audio();

      // Set up event listeners for the audio element
      audioRef.current.addEventListener("play", () => setIsPlaying(true));
      audioRef.current.addEventListener("pause", () => setIsPlaying(false));
      audioRef.current.addEventListener("ended", () => {
        setIsPlaying(false);
        onClose();
      });
      audioRef.current.addEventListener("timeupdate", handleTimeUpdate);
      audioRef.current.addEventListener("loadedmetadata", handleLoadedMetadata);
    }

    // Update audio source if it changed
    if (audioRef.current.src !== currentTrack.src) {
      audioRef.current.src = currentTrack.src;
      audioRef.current.load();
    }

    // Play the audio
    const playPromise = audioRef.current.play();

    // Handle autoplay restrictions
    if (playPromise !== undefined) {
      playPromise.catch(error => {
        console.error("Autoplay prevented:", error);
        setIsPlaying(false);
      });
    }

    // Update volume & muted state
    audioRef.current.volume = isMuted ? 0 : volume / 100;

    // Cleanup function
    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener("play", () => setIsPlaying(true));
        audioRef.current.removeEventListener("pause", () => setIsPlaying(false));
        audioRef.current.removeEventListener("ended", () => {
          setIsPlaying(false);
          onClose();
        });
        audioRef.current.removeEventListener("timeupdate", handleTimeUpdate);
        audioRef.current.removeEventListener("loadedmetadata", handleLoadedMetadata);
        audioRef.current.pause();
      }
    };
  }, [currentTrack?.src, isMuted, volume, handleTimeUpdate, handleLoadedMetadata, onClose]);

  // Update volume when it changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume / 100;
    }
  }, [volume, isMuted]);

  // Format time (seconds to MM:SS)
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Don't render anything if no track is selected
  if (!currentTrack) {
    return null;
  }

  return (
    <div className="border-t bg-background/95 backdrop-blur-sm p-3 shrink-0">
      <div className="flex items-center gap-3">
        {/* Play/Pause Button */}
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "h-8 w-8 rounded-full shrink-0 transition-all duration-200",
            isPlaying
              ? "bg-primary text-primary-foreground hover:bg-primary/90"
              : "hover:bg-accent"
          )}
          onClick={handlePlayPause}
          aria-label={isPlaying ? "Pause" : "Play"}
        >
          {isPlaying ? (
            <Pause className="h-4 w-4" />
          ) : (
            <Play className="h-4 w-4 ml-0.5" />
          )}
        </Button>

        {/* Track Info */}
        <div className="flex-1 min-w-0">
          <div className="truncate font-medium text-sm">
            {currentTrack.title}
          </div>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <span>{formatTime(currentTime)}</span>
            <span>/</span>
            <span>{formatTime(duration)}</span>
            {currentTrack.artist && (
              <>
                <span>•</span>
                <span>{currentTrack.artist}</span>
              </>
            )}
          </div>
        </div>

        {/* Progress Bar - Hidden on mobile */}
        <div className="hidden md:flex flex-1 max-w-32 items-center">
          <Slider
            value={[currentTime]}
            min={0}
            max={duration || 100}
            step={0.1}
            onValueChange={handleSeek}
            className="cursor-pointer"
            aria-label="Seek audio position"
          />
        </div>

        {/* Volume Controls */}
        <div className="flex items-center gap-2 shrink-0">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={handleVolumeToggle}
            aria-label={isMuted ? "Unmute" : "Mute"}
          >
            {isMuted ? (
              <VolumeX className="h-3 w-3" />
            ) : (
              <Volume2 className="h-3 w-3" />
            )}
          </Button>
          <Slider
            value={[isMuted ? 0 : volume]}
            min={0}
            max={100}
            step={1}
            onValueChange={(value) => {
              setVolume(value[0]);
              if (value[0] > 0 && isMuted) {
                setIsMuted(false);
              }
            }}
            className="w-16"
            aria-label="Volume"
          />
        </div>

        {/* Close Button */}
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6"
          onClick={onClose}
          aria-label="Close preview"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>

      {/* Mobile Progress Bar */}
      <div className="md:hidden mt-2">
        <Slider
          value={[currentTime]}
          min={0}
          max={duration || 100}
          step={0.1}
          onValueChange={handleSeek}
          className="cursor-pointer"
          aria-label="Seek audio position"
        />
      </div>
    </div>
  );
}

export function AddItemsDialog({
  open,
  onOpenChange,
  type,
  searchQuery,
  onSearchChange,
  availableItems,
  selectedIds,
  onSelect,
  onSelectAll,
  onAdd,
  currentPlaylist
}: AddItemsDialogProps) {
  const [sortBy, setSortBy] = useState<"name" | "artist">("name");
  const [isPending, setIsPending] = useState(false);
  const [currentPreviewTrack, setCurrentPreviewTrack] = useState<{ id: string; title: string; src: string; artist?: string } | null>(null);
  const [selectedGenres, setSelectedGenres] = useState<string[]>([]);
  const [selectedRating, setSelectedRating] = useState<number | null>(null);


  // Get all available genres from music items
  const allAvailableGenres = useMemo(() => {
    if (type !== "music") return [];
    const genreSet = new Set<string>();
    availableItems.forEach(item => {
      if ("genres" in item && item.genres) {
        item.genres.forEach(genre => genreSet.add(genre));
      }
    });
    return Array.from(genreSet).sort();
  }, [availableItems, type]);

  // Get playlist-level genres (genres assigned directly to the playlist)
  const playlistGenres = useMemo(() => {
    if (!currentPlaylist?.genres) return [];
    return [...currentPlaylist.genres].sort();
  }, [currentPlaylist?.genres]);

  // Get active genres from current playlist (only from music tracks actually in the playlist)
  const musicTrackGenres = useMemo(() => {
    if (!currentPlaylist || !currentPlaylist.musics) return [];
    const genreSet = new Set<string>();

    // Only add genres from music tracks that are actually in the playlist
    currentPlaylist.musics.forEach(music => {
      if (music.genres) {
        music.genres.forEach(genre => genreSet.add(genre));
      }
    });

    return Array.from(genreSet).sort();
  }, [currentPlaylist]);

  // Combined active genres (playlist-level + music track genres)
  const activeGenres = useMemo(() => {
    const combined = [...new Set([...playlistGenres, ...musicTrackGenres])];
    return combined.sort();
  }, [playlistGenres, musicTrackGenres]);

  // Combine and deduplicate all genres, with active genres first
  const allGenres = useMemo(() => {
    const combined = [...new Set([...activeGenres, ...allAvailableGenres])];
    return combined.sort();
  }, [activeGenres, allAvailableGenres]);

  // Pre-select playlist genres when dialog opens (prioritize playlist-level genres)
  useEffect(() => {
    if (open) {
      if (playlistGenres.length > 0) {
        // Pre-filter by playlist-level genres first
        setSelectedGenres(playlistGenres);
      } else if (musicTrackGenres.length > 0) {
        // Fallback to music track genres if no playlist-level genres
        setSelectedGenres(musicTrackGenres);
      } else {
        setSelectedGenres([]);
      }
    }
  }, [open, playlistGenres, musicTrackGenres]);

  // Filter items based on search query, selected genres, and rating
  const filteredItems = useMemo(() => {
    return availableItems.filter(item => {
      // Search filter
      const matchesSearch = searchQuery === "" ||
        item.title.toLowerCase().includes(searchQuery.toLowerCase());

      // Genre filter (only for music)
      let matchesGenre = true;
      if (type === "music" && selectedGenres.length > 0) {
        if ("genres" in item && item.genres) {
          // AND logic: item must have ALL selected genres
          matchesGenre = selectedGenres.every(selectedGenre =>
            item.genres!.includes(selectedGenre)
          );
        } else {
          // If item has no genres, it doesn't match any genre filter
          matchesGenre = false;
        }
      }

      // Rating filter (only for music)
      let matchesRating = true;
      if (type === "music" && selectedRating !== null) {
        if ("rating" in item && item.rating) {
          // Filter by minimum rating (items with rating >= selectedRating)
          matchesRating = item.rating >= selectedRating;
        } else {
          // If item has no rating, it doesn't match rating filter
          matchesRating = false;
        }
      }

      return matchesSearch && matchesGenre && matchesRating;
    });
  }, [availableItems, searchQuery, selectedGenres, selectedRating, type]);

  // Sort filtered items based on selected sort option
  const sortedItems = [...filteredItems].sort((a, b) => {
    if (sortBy === "name") {
      return a.title.localeCompare(b.title);
    } else if (sortBy === "artist" && type === "music") {
      const artistA = (a as Music).artist || "Unknown";
      const artistB = (b as Music).artist || "Unknown";
      return artistA.localeCompare(artistB);
    }
    return 0;
  });

  // Get unique artists for filtering (only for music)
  const getCategories = () => {
    if (type === "music") {
      const artists = Array.from(new Set(availableItems.map(item => (item as Music).artist || "Unknown")));
      return artists.sort();
    }
    return [];
  };

  const categories = getCategories();

  const handleAddItems = async () => {
    setIsPending(true);
    try {
      await onAdd();
    } finally {
      setIsPending(false);
    }
  };

  const handlePlayAudio = (item: Music | Video, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (currentPreviewTrack?.id === item.id) {
      // If already playing this item, stop it
      setCurrentPreviewTrack(null);
    } else {
      // Play the selected item
      if (type === "music" && "src" in item && item.src) {
        setCurrentPreviewTrack({
          id: item.id,
          title: item.title,
          src: item.src,
          artist: "artist" in item ? item.artist : undefined
        });
      }
    }
  };

  const handleClosePreview = () => {
    setCurrentPreviewTrack(null);
  };

  // Genre filter helper functions
  const handleGenreToggle = (genre: string) => {
    setSelectedGenres(prev =>
      prev.includes(genre)
        ? prev.filter(g => g !== genre)
        : [...prev, genre]
    );
  };

  const clearFilters = () => {
    setSelectedGenres([]);
    setSelectedRating(null);
  };

  const clearGenreFilter = () => {
    setSelectedGenres([]);
  };

  const handleRatingFilter = (rating: number) => {
    setSelectedRating(prev => prev === rating ? null : rating);
  };

  const getGenreColor = (isSelected: boolean = false) => {
    if (isSelected) {
      // Red tones for selected genres (user preference)
      return 'bg-red-100 text-red-800 dark:bg-red-900/40 dark:text-red-300 border-red-300 dark:border-red-700';
    }

    // Default styling for unselected genres (gray)
    return 'hover:bg-muted border-muted-foreground/20 hover:border-border hover:text-foreground';
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md md:max-w-lg lg:max-w-5xl h-[85vh] max-h-[800px] flex flex-col">
        <DialogHeader className="shrink-0">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30">
              {type === "music" ? (
                <Music2 className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              ) : (
                <Video className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              )}
            </div>
            <div>
              <DialogTitle className="text-xl">
                {type === "music" ? "Add Music to Playlist Admin" : "Add Videos to Playlist Admin"}
              </DialogTitle>
              <DialogDescription className="mt-1">
                Select items to add to your music playlist
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 flex flex-col space-y-4 py-4 min-h-0">
          {/* Search and filter bar */}
          <div className="flex flex-col sm:flex-row gap-3 shrink-0">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={`Search ${type === "music" ? "music" : "videos"}...`}
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-9 pr-10"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8"
                  onClick={() => onSearchChange("")}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="flex items-center gap-2">
              <DropdownMenu modal={false}>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-1">
                    <Filter className="h-4 w-4" />
                    Sort
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup value={sortBy} onValueChange={(value) => setSortBy(value as "name" | "artist")}>
                    <DropdownMenuRadioItem value="name">Name</DropdownMenuRadioItem>
                    {type === "music" && (
                      <DropdownMenuRadioItem value="artist">Artist</DropdownMenuRadioItem>
                    )}
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>

              <Button
                variant="outline"
                size="sm"
                disabled={filteredItems.length === 0}
                onClick={onSelectAll}
                className="gap-1"
              >
                <CheckCircle2 className="h-4 w-4" />
                Select All
              </Button>
            </div>
          </div>

          {/* Filters (only for music) */}
          {type === "music" && (allGenres.length > 0 || availableItems.some(item => "rating" in item && item.rating)) && (
            <div className="space-y-4 shrink-0">
              {/* Genre Filters */}
              {allGenres.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Filter className="h-3.5 w-3.5 text-muted-foreground" />
                      <span className="text-sm font-medium">Genres</span>
                      {selectedGenres.length > 1 && (
                        <span className="text-xs text-muted-foreground ml-1">(must have all)</span>
                      )}
                      {selectedGenres.length > 0 && (
                        <Badge variant="secondary" className="text-xs h-5">
                          {selectedGenres.length}
                        </Badge>
                      )}
                      {activeGenres.length > 0 && (
                        <span className="text-xs text-muted-foreground ml-1">● = playlist genre, ◐ = in tracks</span>
                      )}
                    </div>
                    <div className="flex gap-1">
                      {selectedGenres.length > 0 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={clearGenreFilter}
                          className="h-7 px-2 text-xs"
                        >
                          <X className="h-3 w-3 mr-1" />
                          Clear Genres
                        </Button>
                      )}
                      {(selectedGenres.length > 0 || selectedRating !== null) && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={clearFilters}
                          className="h-7 px-2 text-xs"
                        >
                          <X className="h-3 w-3 mr-1" />
                          Clear All
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-1.5">
                    {allGenres.map(genre => {
                      const isSelected = selectedGenres.includes(genre);
                      const isPlaylistGenre = playlistGenres.includes(genre);
                      const isMusicTrackGenre = musicTrackGenres.includes(genre);

                      return (
                        <Badge
                          key={genre}
                          variant={isSelected ? "default" : "outline"}
                          className={cn(
                            "cursor-pointer transition-all duration-200 hover:scale-105 text-xs px-2 py-1 h-6",
                            getGenreColor(isSelected)
                          )}
                          onClick={() => handleGenreToggle(genre)}
                        >
                          {genre}
                          {isPlaylistGenre && (
                            <span className="ml-1 text-[10px] opacity-80 font-bold">●</span>
                          )}
                          {!isPlaylistGenre && isMusicTrackGenre && (
                            <span className="ml-1 text-[10px] opacity-80 font-bold">◐</span>
                          )}
                        </Badge>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Rating Filter */}
              {availableItems.some(item => "rating" in item && item.rating) && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="p-1 rounded-md bg-yellow-500/10">
                      <Star className="h-3.5 w-3.5 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <span className="text-sm font-medium">Minimum Rating</span>
                    {selectedRating !== null && (
                      <Badge variant="secondary" className="text-xs h-5">
                        {selectedRating}+ ★
                      </Badge>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    {[1, 2, 3, 4, 5].map(rating => (
                      <Button
                        key={rating}
                        variant="ghost"
                        size="sm"
                        className={cn(
                          "h-8 px-2 transition-all duration-200",
                          selectedRating === rating
                            ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/40 dark:text-yellow-300 border-yellow-300 dark:border-yellow-700"
                            : "hover:bg-muted"
                        )}
                        onClick={() => handleRatingFilter(rating)}
                      >
                        <Star className={cn(
                          "h-4 w-4 mr-1",
                          selectedRating === rating
                            ? "fill-yellow-400 text-yellow-400"
                            : "text-muted-foreground"
                        )} />
                        {rating}+
                      </Button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Status bar */}
          <div className="flex items-center justify-between bg-muted/50 p-2 rounded-md text-sm shrink-0">
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {filteredItems.length} of {availableItems.length} {type === "music" ? "music tracks" : "videos"} shown
              </Badge>

              {searchQuery && (
                <Badge variant="outline" className="flex items-center gap-1">
                  Search: {searchQuery}
                </Badge>
              )}

              {type === "music" && selectedGenres.length > 0 && (
                <Badge variant="outline" className="flex items-center gap-1">
                  Genres: {selectedGenres.length}
                </Badge>
              )}

              {type === "music" && selectedRating !== null && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  {selectedRating}+ rating
                </Badge>
              )}
            </div>

            <Badge variant="secondary" className="bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400">
              {selectedIds.length} selected
            </Badge>
          </div>

          {/* Categories tabs (only for music) */}
          {type === "music" && categories.length > 1 && (
            <div className="shrink-0">
              <Tabs defaultValue="all" className="w-full">
                <TabsList className="w-full h-9 flex overflow-x-auto">
                  <TabsTrigger value="all" className="flex-shrink-0">All</TabsTrigger>
                  {categories.map((category) => (
                    <TabsTrigger key={category} value={category} className="flex-shrink-0">
                      {category}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </div>
          )}

          {/* Items list */}
          <div className="flex-1 min-h-0">
            <ScrollArea className="h-full rounded-md border">
              {sortedItems.length === 0 ? (
                <div className="text-center py-16 text-muted-foreground">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-muted mb-4">
                    {type === "music" ? (
                      <Music2 className="h-6 w-6 text-muted-foreground" />
                    ) : (
                      <Video className="h-6 w-6 text-muted-foreground" />
                    )}
                  </div>
                  <h3 className="text-lg font-medium mb-1">No items found</h3>
                  <p className="text-sm max-w-md mx-auto">
                    {availableItems.length === 0
                      ? `No ${type === "music" ? "music" : "videos"} available to add to this playlist`
                      : searchQuery || (type === "music" && (selectedGenres.length > 0 || selectedRating !== null))
                      ? `No ${type === "music" ? "music" : "videos"} matches your search or filter criteria`
                      : `No ${type === "music" ? "music" : "videos"} found`}
                  </p>
                  {(searchQuery || (type === "music" && (selectedGenres.length > 0 || selectedRating !== null))) && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        onSearchChange("");
                        clearFilters();
                      }}
                      className="mt-3"
                    >
                      <X className="h-3 w-3 mr-1" />
                      Clear Filters
                    </Button>
                  )}
                </div>
              ) : (
                <div className="divide-y">
                  {sortedItems.map((item) => {
                    const isSelected = selectedIds.includes(item.id);
                    const isCurrentlyPlaying = currentPreviewTrack?.id === item.id;
                    const hasAudioSrc = type === "music" && "src" in item && item.src;
                    
                    return (
                      <div
                        key={item.id}
                        className={`group flex items-center space-x-3 p-3 hover:bg-muted/50 ${
                          isSelected ? "bg-primary/5" : ""
                        }`}
                      >
                        <Checkbox
                          id={`select-${item.id}`}
                          checked={isSelected}
                          onCheckedChange={(checked) => onSelect(item.id, !!checked)}
                        />
                        
                        <div className="flex items-center space-x-3 flex-1">
                          {type === "video" && "thumbnail" in item && item.thumbnail && (
                            <div className="w-16 h-9 overflow-hidden rounded bg-muted relative flex-shrink-0">
                              <Image
                                src={item.thumbnail}
                                alt={item.title}
                                fill
                                className="object-cover"
                                sizes="(max-width: 768px) 100vw, 25vw"
                              />
                            </div>
                          )}
                          
                          <div className="flex flex-col flex-1">
                            <label
                              htmlFor={`select-${item.id}`}
                              className="text-sm font-medium cursor-pointer"
                            >
                              {item.title}
                            </label>
                            {type === "music" && "artist" in item && item.artist && (
                              <span className="text-xs text-muted-foreground">
                                {item.artist}
                              </span>
                            )}

                            {/* Genres and Rating for music */}
                            {type === "music" && (
                              <div className="flex items-center gap-2 mt-1 flex-wrap">
                                {/* Genres - Show all genres without truncation */}
                                {"genres" in item && item.genres && item.genres.length > 0 && (
                                  <div className="flex flex-wrap gap-1">
                                    {item.genres.map((genre, index) => {
                                      const isSelectedGenre = selectedGenres.includes(genre);
                                      return (
                                        <Badge
                                          key={index}
                                          variant={isSelectedGenre ? "default" : "secondary"}
                                          className={cn(
                                            "text-[10px] px-1.5 py-0.5 h-4",
                                            isSelectedGenre
                                              ? "bg-red-100 text-red-800 dark:bg-red-900/40 dark:text-red-300 border-red-300 dark:border-red-700"
                                              : "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
                                          )}
                                        >
                                          {genre}
                                        </Badge>
                                      );
                                    })}
                                  </div>
                                )}

                                {/* Rating */}
                                {"rating" in item && item.rating && (
                                  <div className="flex items-center gap-1">
                                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                    <span className="text-xs text-muted-foreground">
                                      {item.rating.toFixed(1)}
                                    </span>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Play button - only show for music with src */}
                        {hasAudioSrc && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className={`h-8 w-8 shrink-0 transition-all duration-200 ${
                              isCurrentlyPlaying 
                                ? "opacity-100 bg-primary/10 text-primary" 
                                : "opacity-0 group-hover:opacity-100 hover:bg-accent"
                            }`}
                            onClick={(e) => handlePlayAudio(item, e)}
                            aria-label={isCurrentlyPlaying ? "Stop playing" : "Play audio"}
                          >
                            {isCurrentlyPlaying ? (
                              <Pause className="h-4 w-4" />
                            ) : (
                              <Play className="h-4 w-4" />
                            )}
                          </Button>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </ScrollArea>
          </div>
        </div>

        {/* Audio Preview Player - Sticky Footer */}
        {currentPreviewTrack && (
          <DialogAudioPlayer
            currentTrack={currentPreviewTrack}
            onClose={handleClosePreview}
          />
        )}

        <DialogFooter className="sm:justify-between flex flex-row sm:space-x-2 shrink-0">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          
          <div className="flex space-x-2">
            <Button
              variant="default"
              onClick={handleAddItems}
              disabled={selectedIds.length === 0 || isPending}
              className="gap-1"
            >
              {isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Check className="h-4 w-4" />
              )}
              Add {selectedIds.length} {selectedIds.length === 1 ? (type === "music" ? "track" : "video") : (type === "music" ? "tracks" : "videos")}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}