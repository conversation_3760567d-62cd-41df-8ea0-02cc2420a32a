'use client';

import { useEffect, useCallback, useState, useMemo, memo } from 'react';
import { VideoBackground } from '@/app/timer/_components/video-background';
import { NavigationControls } from '@/app/timer/_components/navigation-controls';
import { usePomodoroStore } from '@/lib/pomodoro-store';
import { useAudioStore } from '@/lib/audio-store';
import { useRouter } from 'next/navigation';
import { Loader, Home, Maximize } from 'lucide-react';

import { FadeIn } from '@/components/animations/fade-in';
import { AnimatePresence, motion } from 'framer-motion';
import { TimerDisplay } from './_components/timer-display/timer-display';
import { Button } from '@/components/ui/button';
import { NotificationPermissionPrompt } from './_components/notification-permission-prompt';
import { MusicControlWrapper } from '@/components/audio/music-control-wrapper';

// Create a standalone timer container to isolate timer updates
const TimerDisplayContainer = memo(function TimerDisplayContainer() {
  return <TimerDisplay />;
});

export default function TimerPage() {
  const selectedVideo = usePomodoroStore((state) => state.selectedVideo);
  const { selectedAudios, useVideoDefaultAudio } = useAudioStore();
  const router = useRouter();
  const [isExiting, setIsExiting] = useState(false);
  const [notificationPromptDismissed, setNotificationPromptDismissed] = useState(false);

  // Get necessary store functions and state
  const resetToPomodoro = usePomodoroStore((state) => state.resetToPomodoro);
  const isRunning = usePomodoroStore((state) => state.isRunning);
  const buildNavigableContent = usePomodoroStore((state) => state.buildNavigableContent);
  const videos = usePomodoroStore((state) => state.videos);

  // Always ensure we start in pomodoro/focus phase when timer page loads
  useEffect(() => {
    // Always reset to pomodoro phase when the timer page loads
    // console.log('Timer page loaded - resetting to pomodoro phase');
    resetToPomodoro();
  }, [resetToPomodoro]); // Include resetToPomodoro in dependency array

  // Build navigable content when videos are available
  useEffect(() => {
    if (videos.length > 0) {
      buildNavigableContent();
    }
  }, [videos, buildNavigableContent]);

  // Handle exit fullscreen before navigation with animation
  const handleBackNavigation = useCallback(() => {
    // Start exit animation
    setIsExiting(true);

    // Function to handle the actual navigation after animations
    const navigateToHome = () => {
      // Check if we're in fullscreen mode
      if (document.fullscreenElement) {
        // Exit fullscreen first
        document.exitFullscreen()
          .then(() => {
            // Then navigate back home after fullscreen exit is complete
            router.push('/');
          })
          .catch((err) => {
            console.error('Error exiting fullscreen:', err);
            // If there's an error exiting fullscreen, navigate anyway
            router.push('/');
          });
      } else {
        // Not in fullscreen, just navigate directly
        router.push('/');
      }
    };

    // Wait for animation to complete before navigation
    setTimeout(navigateToHome, 650); // Slightly longer than animation duration to ensure completion
  }, [router]);

  // Redirect to home if no video is selected
  useEffect(() => {
    if (!selectedVideo) {
      router.push('/');
    }
  }, [selectedVideo, router]);

  // Validate audio selections
  useEffect(() => {
    if (!useVideoDefaultAudio && selectedAudios.length === 0) {
      // If custom audio is enabled but no tracks are selected, use video audio instead
      // This prevents having no audio at all
      // We could add a UI notification here if needed
    }
  }, [useVideoDefaultAudio, selectedAudios.length]);

  // Get the autoFullscreen setting from the store
  const autoFullscreen = usePomodoroStore((state) => state.autoFullscreen);
  const setIsFullscreen = usePomodoroStore((state) => state.setIsFullscreen);

  // Add a class to the body when the timer page loads and handle auto-fullscreen
  useEffect(() => {
    // Add classes for scrollbar management
    document.body.classList.add('has-timer');

    // Clean up when component unmounts
    return () => {
      document.body.classList.remove('has-timer');
    };
  }, []);

  // Handle auto-fullscreen with user interaction requirement
  const [showFullscreenPrompt, setShowFullscreenPrompt] = useState(false);

  useEffect(() => {
    // Check if auto-fullscreen is enabled but we're not already in fullscreen mode
    if (autoFullscreen && typeof document !== 'undefined' && !document.fullscreenElement) {
      // Instead of automatically requesting fullscreen, show a prompt
      setShowFullscreenPrompt(true);
    }
  }, [autoFullscreen]);

  // Memoize the VideoBackground component to prevent re-renders on timer ticks
  const memoizedVideoBackground = useMemo(() => {
    if (!selectedVideo) return null;
    return <VideoBackground onBackNavigation={handleBackNavigation} />;
  }, [selectedVideo, handleBackNavigation]);



  if (!selectedVideo) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <FadeIn customKey="loading-state">
          <div className="text-center">
            <Loader className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-muted-foreground">Redirecting to home page...</p>
          </div>
        </FadeIn>
      </div>
    );
  }

  // Define the spinning animation variants
  const spinTransition = {
    loop: 3,
    ease: "linear",
    duration: 1
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key="timer-page"
        initial={{ opacity: 1 }}
        animate={{ opacity: 1 }}
        exit={{
          opacity: 0,
          filter: "blur(12px)",
          scale: 0.95,
        }}
        transition={{
          duration: 0.5,
          ease: "easeInOut",
          opacity: { duration: 0.5 },
          filter: { duration: 0.4 },
          scale: { duration: 0.5 }
        }}
        className="relative min-h-screen w-full overflow-hidden timer-page-container"
      >
        {/* Elegant exit transition overlay */}
        {isExiting && (
          <motion.div
            initial={{
              opacity: 0,
              backdropFilter: "blur(0px)"
            }}
            animate={{
              opacity: 1,
              backdropFilter: "blur(8px)"
            }}
            exit={{
              opacity: 0,
              backdropFilter: "blur(0px)"
            }}
            transition={{
              duration: 0.4,
              ease: "easeOut"
            }}
            className="fixed inset-0 bg-background/10 backdrop-blur-md z-50 pointer-events-none flex flex-col items-center justify-center"
          >
            {/* Add a simpler spinning icon */}
            <motion.div
               className="mb-6" // Add margin bottom to space it from the text
               animate={{ rotate: 360 }}
               transition={spinTransition} // Use the existing spin transition
            >
               <Home className="h-10 w-10 text-white" /> {/* Adjust size and color as needed */}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="flex flex-col items-center"
            >
              <h3 className="text-xl font-medium tracking-wide bg-clip-text text-transparent bg-gradient-to-r from-white/90 to-white/60 mb-2">
                Returning to Home
              </h3>
            </motion.div>
          </motion.div>
        )}

        {/* Video background (z-0) */}
        {memoizedVideoBackground}
        
        {/* Navigation controls (z-50 - higher than timer which is z-20) */}
        <NavigationControls onBackNavigation={handleBackNavigation} />

        {/* Timer display (z-20) */}
        <TimerDisplayContainer />

        {/* Music Control - Moved outside video background to fix z-index stacking */}
        <MusicControlWrapper />

        {/* Notification permission prompt */}
        {!notificationPromptDismissed && (
          <NotificationPermissionPrompt
            isTimerRunning={isRunning}
            onDismiss={() => setNotificationPromptDismissed(true)}
          />
        )}

        {/* Fullscreen prompt */}
        {showFullscreenPrompt && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="fixed bottom-4 sm:bottom-8 left-4 right-4 sm:left-1/2 sm:right-auto sm:transform sm:-translate-x-1/2 z-50 bg-background/80 backdrop-blur-md px-3 sm:px-4 py-3 rounded-lg shadow-lg border border-border flex flex-col sm:flex-row items-center gap-3 sm:w-auto w-full"
          >
            <Maximize className="h-5 w-5 text-primary flex-shrink-0" />
            <div className="flex-1 text-center sm:text-left">
              <p className="text-sm font-medium">Enter fullscreen mode?</p>
              <p className="text-xs text-muted-foreground">For the best experience</p>
            </div>
            <div className="flex gap-2 w-full sm:w-auto">
              <Button
                variant="ghost"
                size="sm"
                className="flex-1 sm:flex-none"
                onClick={() => setShowFullscreenPrompt(false)}
              >
                Dismiss
              </Button>
              <Button
                variant="default"
                size="sm"
                className="flex-1 sm:flex-none"
                onClick={() => {
                  // Request fullscreen with user interaction
                  document.documentElement.requestFullscreen()
                    .then(() => {
                      setIsFullscreen(true);
                      setShowFullscreenPrompt(false);
                    })
                    .catch(err => {
                      console.error(`Error attempting to enable fullscreen: ${err.message}`);
                      setShowFullscreenPrompt(false);
                    });
                }}
              >
                Enter Fullscreen
              </Button>
            </div>
          </motion.div>
        )}
      </motion.div>
    </AnimatePresence>
  );
}